"use client";

import { generateFileUrl } from "@/lib/utils";
import React, { useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";

interface ProfileDropdownProps {
  profiles: any[];
  onSelect: (profile: any) => void;
  inputRef: React.RefObject<HTMLInputElement>;
  isVisible: boolean;
  loading?: boolean;
}

// const generateFileUrl = (postFile: string | undefined): string | undefined => {
//   const baseUrl = process.env.BASE_STORAGE_URL;
//   if (!baseUrl) return undefined;

//   if (!postFile) {
//     return undefined;
//   }

//   if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
//     return postFile;
//   }

//   return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
// };

const ProfileDropdown: React.FC<ProfileDropdownProps> = ({
  profiles,
  onSelect,
  inputRef,
  isVisible,
  loading = false,
}) => {
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState<"top" | "bottom">("bottom");
  const [isMobile, setIsMobile] = useState(false);

  // Detect mobile device on mount
  useEffect(() => {
    const checkIfMobile = () => {
      const userAgent = navigator.userAgent.toLowerCase();
      const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(
        userAgent
      );
      const isSmallScreen = window.innerWidth <= 768;
      setIsMobile(isMobileDevice || isSmallScreen);
    };

    checkIfMobile();
    window.addEventListener("resize", checkIfMobile);

    return () => {
      window.removeEventListener("resize", checkIfMobile);
    };
  }, []);

  useEffect(() => {
    if (!isVisible || !inputRef.current || !dropdownRef.current) return;

    // Position the dropdown intelligently based on available space
    const updatePosition = () => {
      if (!inputRef.current || !dropdownRef.current) return;

      const rect = inputRef.current.getBoundingClientRect();

      // Add a small buffer to ensure there's a gap between input and dropdown
      const buffer = 5;

      // Calculate dropdown height - account for header (28px) and items
      const itemHeight = 60; // Each profile item is approximately 60px tall
      const headerHeight = 28; // Header is approximately 28px tall
      const dropdownHeight = Math.min(
        headerHeight + profiles.length * itemHeight,
        headerHeight + 5 * itemHeight // Max 5 items visible before scrolling
      );

      // Calculate available space
      const spaceBelow = window.innerHeight - rect.bottom - buffer;
      const spaceAbove = rect.top - buffer;

      // Always prefer bottom positioning unless there's insufficient space
      const newPosition = spaceBelow >= dropdownHeight ? "bottom" : "top";

      setPosition(newPosition);

      // Set position styles
      dropdownRef.current.style.position = "fixed";
      dropdownRef.current.style.width = `${rect.width}px`;
      dropdownRef.current.style.left = `${rect.left}px`;

      if (newPosition === "bottom") {
        // Position below the input with a small gap
        dropdownRef.current.style.top = `${rect.bottom + buffer}px`;
        dropdownRef.current.style.bottom = "auto";
        dropdownRef.current.style.maxHeight = `${window.innerHeight - rect.bottom - buffer * 2}px`;
      } else {
        // Position above the input with a small gap
        dropdownRef.current.style.bottom = `${window.innerHeight - rect.top + buffer}px`;
        dropdownRef.current.style.top = "auto";
        dropdownRef.current.style.maxHeight = `${rect.top - buffer * 2}px`;
      }
    };

    // Update position initially and on resize
    updatePosition();
    window.addEventListener("resize", updatePosition);
    window.addEventListener("scroll", updatePosition);

    return () => {
      window.removeEventListener("resize", updatePosition);
      window.removeEventListener("scroll", updatePosition);
    };
  }, [isVisible, inputRef, profiles.length]);

  if (!isVisible) return null;

  // Handle selection with minimal event prevention
  const handleSelect = (profile: any, event: React.MouseEvent) => {
    console.log("ProfileDropdown: handleSelect called with profile:", profile);

    // Only prevent default behavior, allow propagation for now
    event.preventDefault();

    // Call the onSelect callback immediately
    onSelect(profile);

    // On mobile, blur the input to hide keyboard after selection
    if (isMobile && inputRef.current) {
      inputRef.current.blur();
    }
  };

  // Create portal to render the dropdown at the document body level
  return createPortal(
    <div
      ref={dropdownRef}
      className={`bg-white border rounded-md shadow-xl overflow-y-auto transition-all duration-200 animate-in fade-in-50 ${
        position === "top"
          ? "origin-bottom slide-in-from-bottom-2"
          : "origin-top slide-in-from-top-2"
      } ${isMobile ? "mobile-dropdown" : ""}`}
      style={{
        position: "fixed",
        zIndex: 999999,
        pointerEvents: "auto",
      }}
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      {loading ? (
        <div className="px-4 py-3 text-center">
          <div className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          <span className="ml-2 text-sm text-gray-500">Searching profiles...</span>
        </div>
      ) : profiles.length > 0 ? (
        <>
          <div className="sticky top-0 bg-gray-50 px-4 py-2 text-xs font-medium text-gray-500 border-b">
            {isMobile ? "Tap to select a profile" : "Select a profile"}
          </div>
          {profiles.map((profile, index) => (
            <div
              key={profile.id || index}
              className={`px-4 py-3 cursor-pointer transition-colors border-b border-gray-100 last:border-0 flex items-center ${
                isMobile ? "py-4 active:bg-gray-100" : "hover:bg-gray-50"
              }`}
              onClick={(e) => handleSelect(profile, e)}
            >
              <div className="mr-3">
                {profile.avatarSmall ? (
                  <img
                    src={generateFileUrl(profile.avatarSmall) || "/assets/noimg.png"}
                    alt={profile.profile_name}
                    className="w-8 h-8 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
                    <span className="text-xs font-medium text-gray-600">
                      {profile.profile_name?.charAt(0)?.toUpperCase() || "?"}
                    </span>
                  </div>
                )}
              </div>
              <div className="flex-1">
                <div className={`font-medium ${isMobile ? "text-base" : "text-sm"}`}>
                  {profile.profile_name || "Unknown User"}
                </div>
                {profile.location && (
                  <div className={`text-gray-500 ${isMobile ? "text-sm" : "text-xs"}`}>
                    {profile.location}
                  </div>
                )}
              </div>
            </div>
          ))}
        </>
      ) : (
        <div className={`px-4 py-3 ${isMobile ? "text-base" : "text-sm"} text-gray-500`}>
          No profiles found
        </div>
      )}
    </div>,
    document.body
  );
};

export default ProfileDropdown;
